保存学生考试记录
接口：/v1/internship/student/examRecord/save
入参：
```json
{
    //试题及记录
  "examQuestionList": [
    {
      "correctAnswer": "string",//正确答案（如A、A,B）
      "id": 0,
      "isCorrect": 0,//是否正确1:正确 0:错误
      "optionsJson": "string",//ExamOption实体的json字符串，如：[{'A':'xxx','B':'xxx'}]，注意转义字符
      "score": 0,//分数
      "studentAnswer": "string", //学生答案
      "title": "string" //题干（支持富文本）
    }
  ],
  "passStatus": 0, //是否通过（0=否，1=是）
  "planId": 0, // 关联实习计划ID
  "score": 0 // 实际得分
}

```
注意：入参中的optionsJson这个字段是获取试题的选项model `ExamOption` 转为json字符串

返回结果：
```json
{
  "data": 0,
  "resultCode": "string",
  "resultMsg": "string"
}
```