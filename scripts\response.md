安全教育考试详情-安全教育-老师端
接口：/v1/internship/teacher/examQuestion/detail
请求类型：GET
入参：recordId 


```
注意：入参中的optionsJson这个字段是获取试题的选项model `ExamOption` 转为json字符串

返回结果：
```json
{
  "data": {
    "examQuestionList": [
      {
        "correctAnswer": "string",
        "id": 0,
        "isCorrect": 0,
        "optionsJson": "string",
        "score": 0,
        "studentAnswer": "string",
        "title": "string"
      }
    ],
    "id": 0,
    "passStatus": 0,
    "planId": 0,
    "score": 0,
    "studentId": 0,
    "studentName": "string"
  },
  "resultCode": "string",
  "resultMsg": "string"
}
```