/// -----
/// app_router.dart
///
/// 应用路由配置，定义所有页面路由
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/attendance/presentation/screens/attendance_calendar_screen.dart';
import 'package:flutter_demo/features/attendance/presentation/screens/sign_in_screen.dart';
import 'package:flutter_demo/features/data/presentation/screens/data_screen.dart';
import 'package:flutter_demo/features/employment/presentation/screens/employment_report_screen.dart';
import 'package:flutter_demo/features/evaluation/presentation/screens/evaluation_teacher_screen.dart';
import 'package:flutter_demo/features/evaluation/presentation/screens/company_evaluation_screen.dart';
import 'package:flutter_demo/features/exemption/presentation/screens/sign_exemption_screen.dart';
import 'package:flutter_demo/features/grade/presentation/screens/my_grade_screen.dart';
import 'package:flutter_demo/features/home/<USER>/screens/home_screen.dart';
import 'package:flutter_demo/features/home/<USER>/widgets/home_page_content.dart';
import 'package:flutter_demo/features/internship/presentation/screens/free_internship_approval_list_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_application_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_approval_list_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_change_list_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_complaint_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_insurance_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_score_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_student_detail_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_student_list_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_to_job_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/teacher_internship_plan_list_screen.dart';
import 'package:flutter_demo/features/internship/presentation/screens/teacher_internship_plan_detail_screen.dart';
import 'package:flutter_demo/features/leave/presentation/screens/leave_approval_list_screen.dart';
import 'package:flutter_demo/features/notification/presentation/screens/notification_screen.dart';
import 'package:flutter_demo/features/plan/presentation/screens/student_internship_plan_list_screen.dart';
import 'package:flutter_demo/features/profile/presentation/screens/profile_screen.dart';
import 'package:flutter_demo/features/approval/presentation/pages/file_approval_screen.dart';
import 'package:flutter_demo/features/approval/presentation/pages/file_approval_list_screen.dart';
import 'package:flutter_demo/features/report/presentation/screens/student_report_list_screen.dart';
import 'package:flutter_demo/features/report/presentation/screens/teacher_report_approval_list_screen.dart';
import 'package:flutter_demo/features/report/presentation/screens/student_report_write_screen.dart';
import 'package:flutter_demo/features/auth/presentation/pages/identity_verification_screen.dart';
import 'package:flutter_demo/features/safety/presentation/screens/safety_exam_screen.dart';
import 'package:flutter_demo/features/safety/presentation/screens/safety_exam_notice_screen.dart';
import 'package:flutter_demo/features/safety/presentation/screens/safety_warning_screen.dart';
import 'package:flutter_demo/features/safety/presentation/screens/teacher_safety_education_screen.dart';

import 'package:flutter_demo/features/upload/presentation/pages/file_upload_detail_screen.dart';
import 'package:flutter_demo/features/upload/models/file_upload_item.dart' as models;
import 'package:flutter_demo/features/upload/domain/entities/file_requirement.dart';
import 'package:flutter_demo/features/upload/presentation/pages/student_file_upload_screen.dart';
import 'package:flutter_demo/features/approval/presentation/pages/file_approval_preview_screen.dart';
import 'package:flutter_demo/features/approval/domain/entities/student_file_approval.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/presentation/bloc/reset_password/reset_password_bloc.dart';
import '../../features/auth/presentation/pages/login_screen.dart';
import '../../features/auth/presentation/pages/register_screen.dart';
import '../../features/auth/presentation/pages/reset_password_screen.dart';
import '../../features/report/core/enums/report_enums.dart';
import '../../features/splash/presentation/pages/splash_screen.dart';
import '../config/injection/injection.dart';
import 'route_constants.dart';

/// 应用路由配置
///
/// 使用 go_router 进行路由管理
class AppRouter {
  /// 根导航器的键
  static final GlobalKey<NavigatorState> rootNavigatorKey = GlobalKey<NavigatorState>();

  /// Shell导航器的键（用于底部导航栏）
  static final GlobalKey<NavigatorState> shellNavigatorKey = GlobalKey<NavigatorState>();

  /// 创建路由器
  ///
  /// 返回配置好的 GoRouter 实例
  static GoRouter createRouter() {
    return GoRouter(
      navigatorKey: rootNavigatorKey,
      initialLocation: AppRoutes.splash,
      debugLogDiagnostics: kDebugMode,
      routes: [
        // 启动页
        GoRoute(
          path: AppRoutes.splash,
          builder: (context, state) => const SplashScreen(),
        ),

        // 登录页
        GoRoute(
          path: AppRoutes.login,
          builder: (context, state) => const LoginScreen(),
        ),

        // 注册页
        GoRoute(
          path: AppRoutes.register,
          builder: (context, state) => const RegisterScreen(),
        ),

        // 重置密码页面路由
        GoRoute(
          path: AppRoutes.resetPassword,
          builder: (context, state) => BlocProvider(
            create: (context) => getIt<ResetPasswordBloc>(),
            child: const ResetPasswordScreen(),
          ),
        ),

        // 身份验证页
        GoRoute(
          path: AppRoutes.identityVerification,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const IdentityVerificationScreen(),
        ),

        // 实习列表
        GoRoute(
          path: AppRoutes.teacherInternshipList,
          name: AppRoutes.teacherInternshipList,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const InternshipApprovalListScreen(),
        ),

        // 日报
        GoRoute(
          path: AppRoutes.teacherReportDaily,
          name: AppRoutes.teacherReportDaily,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const TeacherReportApprovalListScreen(reportType: ReportType.daily),
        ),
        // 周报
        GoRoute(
          path: AppRoutes.teacherReportWeekly,
          name: AppRoutes.teacherReportWeekly,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const TeacherReportApprovalListScreen(reportType: ReportType.weekly),
        ),
        // 月报
        GoRoute(
          path: AppRoutes.teacherReportMonthly,
          name: AppRoutes.teacherReportMonthly,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const TeacherReportApprovalListScreen(reportType: ReportType.monthly),
        ),
        // 总结
        GoRoute(
          path: AppRoutes.teacherReportSummary,
          name: AppRoutes.teacherReportSummary,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const TeacherReportApprovalListScreen(reportType: ReportType.summary),
        ),
        GoRoute(
          path: AppRoutes.teacherSafetyEdu,
          name: AppRoutes.teacherSafetyEdu,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const TeacherSafetyEducationScreen(),
        ),
        // 实习计划
        GoRoute(
          path: AppRoutes.teacherInternshipPlan,
          name: AppRoutes.teacherInternshipPlan,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const TeacherInternshipPlanListScreen(),
        ),
        // 实习计划详情
        GoRoute(
          path: AppRoutes.teacherInternshipPlanDetail,
          name: AppRoutes.teacherInternshipPlanDetail,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) {
            final planId = state.pathParameters['planId']!;
            return TeacherInternshipPlanDetailScreen(planId: planId);
          },
        ),
        // 安全警告
        GoRoute(
          path: AppRoutes.teacherSafetyWarning,
          name: AppRoutes.teacherSafetyWarning,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const SafetyWarningScreen(),
        ),
        // 教师端实习生
        GoRoute(
          path: AppRoutes.teacherInternshipStudent,
          name: AppRoutes.teacherInternshipStudent,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const InternshipStudentListScreen(),
        ),
        // 免实习
        GoRoute(
          path: AppRoutes.teacherFreeInternshipList,
          name: AppRoutes.teacherFreeInternshipList,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const FreeInternshipApprovalListScreen(),
        ),
        // 免签
        GoRoute(
          path: AppRoutes.teacherSignExemption,
          name: AppRoutes.teacherSignExemption,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const SignExemptionScreen(),
        ),

        // 请假
        GoRoute(
          path: AppRoutes.teacherLeave,
          name: AppRoutes.teacherLeave,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const LeaveApprovalScreen(),
        ),

        // 实习信息变更
        GoRoute(
          path: AppRoutes.teacherInternshipInfoChange,
          name: AppRoutes.teacherInternshipInfoChange,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const InternshipChangeListScreen(),
        ),

        // 实习转就业
        GoRoute(
          path: AppRoutes.teacherToEmployment,
          name: AppRoutes.teacherToEmployment,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const InternshipToJobScreen(),
        ),
        GoRoute(
          path: AppRoutes.teacherInternshipGrade,
          name: AppRoutes.teacherInternshipGrade,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const InternshipScoreScreen(),
        ),
        GoRoute(
          path: AppRoutes.studentInternshipComplaint,
          name: AppRoutes.studentInternshipComplaint,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const InternshipComplaintScreen(),
        ),
        GoRoute(
          path: AppRoutes.studentEvaluationTeacher,
          name: AppRoutes.studentEvaluationTeacher,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const EvaluationTeacherScreen(),
        ),
        GoRoute(
          path: AppRoutes.studentInternshipApplication,
          name: AppRoutes.studentInternshipApplication,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const InternshipApplicationScreen(planId: ''),
        ),
        GoRoute(
          path: AppRoutes.studentEmploymentReporting,
          name: AppRoutes.studentEmploymentReporting,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const EmploymentReportScreen(),
        ),
        GoRoute(
          path: AppRoutes.studentSafetyEducation,
          name: AppRoutes.studentSafetyEducation,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const SafetyExamScreen(),
        ),
        // 安全教育考试须知
        GoRoute(
          path: AppRoutes.safetyExamNotice,
          name: AppRoutes.safetyExamNotice,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const SafetyExamNoticeScreen(),
        ),
        // 安全教育考试
        GoRoute(
          path: AppRoutes.safetyExam,
          name: AppRoutes.safetyExam,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const SafetyExamScreen(),
        ),
        GoRoute(
          path: AppRoutes.studentInternshipPlan,
          name: AppRoutes.studentInternshipPlan,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const StudentInternshipPlanListScreen(),
        ),
        GoRoute(
          path: AppRoutes.studentInternshipInsuranceInfo,
          name: AppRoutes.studentInternshipInsuranceInfo,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const InternshipInsuranceScreen(),
        ),
        GoRoute(
          path: AppRoutes.studentSignIn,
          name: AppRoutes.studentSignIn,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const SignInScreen(),
        ),
        GoRoute(
          path: AppRoutes.studentFileUpload,
          name: AppRoutes.studentFileUpload,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const StudentFileUploadScreen(),
        ),
        GoRoute(
          path: AppRoutes.studentFileUploadDetail,
          name: AppRoutes.studentFileUploadDetail,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) {
            final fileId = state.pathParameters[RouteParams.fileId] ?? '';

            // 尝试从extra参数获取FileRequirement对象
            final fileRequirement = state.extra;

            if (fileRequirement != null && fileRequirement is FileRequirement) {
              // 如果有传递FileRequirement对象，使用它来创建FileUploadItem
              return FileUploadDetailScreen(
                uploadItem: _convertFileRequirementToUploadItem(fileRequirement),
              );
            } else {
              // 如果没有传递对象，使用旧的模拟数据方法（向后兼容）
              return FileUploadDetailScreen(
                uploadItem: _getFileUploadItemById(fileId),
              );
            }
          },
        ),
         GoRoute(
          path: AppRoutes.studentEnterpriseEvaluation,
          name: AppRoutes.studentEnterpriseEvaluation,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const CompanyEvaluationScreen(),
        ),
        GoRoute(
          path: AppRoutes.studentMyGrade,
          name: AppRoutes.studentMyGrade,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const MyGradeScreen(),
        ),
        GoRoute(
           path: AppRoutes.studentSignCalendar,
           name: AppRoutes.studentSignCalendar,
           parentNavigatorKey: rootNavigatorKey,
           builder: (context, state) => const AttendanceCalendarScreen(),
        ),
        GoRoute(
          path: AppRoutes.studentMyInternship,
          name: AppRoutes.studentMyInternship,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const InternshipStudentDetailScreen(studentId: '',),
        ),
        GoRoute(
          path: AppRoutes.notice,
          name: AppRoutes.notice,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const NotificationScreen()
        ),
        // 文件审批
        GoRoute(
          path: AppRoutes.teacherFileApproval,
          name: AppRoutes.teacherFileApproval,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const FileApprovalScreen(),
        ),

        // 文件审批详情（列表页面）
        GoRoute(
          path: AppRoutes.fileApprovalDetail,
          name: AppRoutes.fileApprovalDetail,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) {
            final fileId = state.pathParameters['fileId'] ?? '';
            return FileApprovalListScreen(fileId: fileId);
          },
        ),
        // 报告相关
        GoRoute(
          path: AppRoutes.studentDailyReport,
          name: AppRoutes.studentDailyReport,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const StudentReportListView(type: ReportType.daily),
        ),
        GoRoute(
          path: AppRoutes.studentWeeklyReport,
          name: AppRoutes.studentWeeklyReport,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const StudentReportListView(type: ReportType.weekly),
        ),
        GoRoute(
          path: AppRoutes.studentMonthlyReport,
          name: AppRoutes.studentMonthlyReport,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const StudentReportListView(type: ReportType.monthly),
        ),
        GoRoute(
          path: AppRoutes.studentSummaryReport,
          name: AppRoutes.studentSummaryReport,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const StudentReportListView(type: ReportType.summary),
        ),

        // 写报告页面路由
        GoRoute(
          path: AppRoutes.reportWriteDaily,
          name: AppRoutes.reportWriteDaily,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const ReportWriteScreen(reportType: ReportType.daily),
        ),
        GoRoute(
          path: AppRoutes.reportWriteWeekly,
          name: AppRoutes.reportWriteWeekly,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const ReportWriteScreen(reportType: ReportType.weekly),
        ),
        GoRoute(
          path: AppRoutes.reportWriteMonthly,
          name: AppRoutes.reportWriteMonthly,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const ReportWriteScreen(reportType: ReportType.monthly),
        ),
        GoRoute(
          path: AppRoutes.reportWriteSummary,
          name: AppRoutes.reportWriteSummary,
          parentNavigatorKey: rootNavigatorKey,
          builder: (context, state) => const ReportWriteScreen(reportType: ReportType.summary),
        ),
        // 主页（带底部导航栏）- 使用StatefulShellRoute.indexedStack保持各标签页状态
        StatefulShellRoute.indexedStack(
          builder: (context, state, navigationShell) {
            // 返回带有底部导航栏的主页面，传入navigationShell用于控制导航
            return HomeScreen(navigationShell: navigationShell);
          },
          branches: [
            // 首页分支
            StatefulShellBranch(
              navigatorKey: shellNavigatorKey,
              routes: [
                // 首页
                GoRoute(
                  path: AppRoutes.home,
                  pageBuilder: (context, state) => const NoTransitionPage(
                    child: HomePageContent(),
                  ),
                ),
              ],
            ),

            // 数据页分支
            StatefulShellBranch(
              routes: [
                // 数据页
                GoRoute(
                  path: AppRoutes.data,
                  pageBuilder: (context, state) => const NoTransitionPage(
                    child: DataScreen(),
                  ),
                ),
              ],
            ),

            // 个人中心分支
            StatefulShellBranch(
              routes: [
                // 个人中心
                GoRoute(
                  path: AppRoutes.profile,
                  pageBuilder: (context, state) => const NoTransitionPage(
                    child: ProfileScreen(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
      errorBuilder: (context, state) => ErrorScreen(error: state.error),
    );
  }

  /// 根据文件ID获取文件上传项（模拟数据）
  static models.FileUploadItem _getFileUploadItemById(String fileId) {
    // 模拟数据，实际应该从数据源获取
    final Map<String, models.FileUploadItem> mockData = {
      'tripartite_agreement': models.FileUploadItem(
        id: 'tripartite_agreement',
        fileName: '三方协议',
        iconData: 'assets/images/upload_blue_doc_icon.png',
        iconColor: const Color(0xFF3B8BFF),
        status: models.FileUploadStatus.notUploaded,
        statusColor: const Color(0xFFE5E5E5),
        statusTextColor: const Color(0xFFB0B0B0),
        courseInfo: '2021级市场销售2023-2024实习学年第二学期岗位实习',
        planId: '8',
        fileType: 'tripartite_agreement',
        fileCode: 1,
        templateUrl: 'https://example.com/template.docx',
        description: '这里是实习计划创建相关文件上传的描述文字',
      ),
      'parent_notice': models.FileUploadItem(
        id: 'parent_notice',
        fileName: '告家长通知书',
        iconData: 'assets/images/upload_pink_doc_icon.png',
        iconColor: const Color(0xFFFF4D8C),
        status: models.FileUploadStatus.approved,
        statusColor: const Color(0xFFE6FFF3),
        statusTextColor: const Color(0xFF1AC29A),
        courseInfo: '2021级市场销售2023-2024实习学年第二学期岗位实习',
        planId: '8',
        fileType: 'parent_notice',
        fileCode: 2,
        uploadedFiles: [
          models.UploadedFile(
            fileName: 'notice1.pdf',
            filePath: '/path/to/notice1.pdf',
            previewImage: 'assets/images/file_preview_1.png',
            uploadTime: DateTime.now().subtract(const Duration(days: 2)),
          ),
          models.UploadedFile(
            fileName: 'notice2.pdf',
            filePath: '/path/to/notice2.pdf',
            previewImage: 'assets/images/file_preview_2.png',
            uploadTime: DateTime.now().subtract(const Duration(days: 1)),
          ),
          models.UploadedFile(
            fileName: 'notice3.pdf',
            filePath: '/path/to/notice3.pdf',
            previewImage: 'assets/images/file_preview_3.png',
            uploadTime: DateTime.now(),
          ),
        ],
        approvalInfo: models.ApprovalInfo(
          name: '冯顶老师',
          position: '班主任',
          avatar: 'assets/images/teacher_avatar.png',
          status: models.FileUploadStatus.approved,
        ),
      ),
      'insurance': models.FileUploadItem(
        id: 'insurance',
        fileName: '实习保险单',
        iconData: 'assets/images/upload_purple_doc_icon.png',
        iconColor: const Color(0xFF8B4DFF),
        status: models.FileUploadStatus.rejected,
        statusColor: const Color(0xFFFFE6E6),
        statusTextColor: const Color(0xFFFF4D4F),
        courseInfo: '2021级市场销售2023-2024实习学年第二学期岗位实习',
        planId: '8',
        fileType: 'insurance',
        fileCode: 3,
        uploadedFiles: [
          models.UploadedFile(
            fileName: 'insurance1.pdf',
            filePath: '/path/to/insurance1.pdf',
            previewImage: 'assets/images/file_preview_1.png',
            uploadTime: DateTime.now().subtract(const Duration(days: 3)),
          ),
          models.UploadedFile(
            fileName: 'insurance2.pdf',
            filePath: '/path/to/insurance2.pdf',
            previewImage: 'assets/images/file_preview_2.png',
            uploadTime: DateTime.now().subtract(const Duration(days: 2)),
          ),
          models.UploadedFile(
            fileName: 'insurance3.pdf',
            filePath: '/path/to/insurance3.pdf',
            previewImage: 'assets/images/file_preview_3.png',
            uploadTime: DateTime.now().subtract(const Duration(days: 1)),
          ),
        ],
        approvalInfo: models.ApprovalInfo(
          name: '冯顶老师',
          position: '班主任',
          avatar: 'assets/images/teacher_avatar.png',
          status: models.FileUploadStatus.rejected,
          comment: '文件不清晰，请重新上传',
        ),
      ),
    };

    return mockData[fileId] ?? mockData['tripartite_agreement']!;
  }

  /// 将FileRequirement转换为FileUploadItem
  static models.FileUploadItem _convertFileRequirementToUploadItem(FileRequirement fileRequirement) {
    // 将FileStatus转换为FileUploadStatus
    models.FileUploadStatus uploadStatus;
    switch (fileRequirement.fileStatus) {
      case FileStatus.notUploaded:
        uploadStatus = models.FileUploadStatus.notUploaded;
        break;
      case FileStatus.uploaded:
        uploadStatus = models.FileUploadStatus.uploaded;
        break;
      case FileStatus.reviewed:
        uploadStatus = models.FileUploadStatus.approved;
        break;
      case FileStatus.rejected:
        uploadStatus = models.FileUploadStatus.rejected;
        break;
    }

    // 根据状态设置审批信息
    models.ApprovalInfo? approvalInfo;
    if (uploadStatus != models.FileUploadStatus.notUploaded) {
      approvalInfo = models.ApprovalInfo(
        name: '冯项老师',
        position: '班主任',
        avatar: 'assets/images/teacher_avatar.png',
        status: uploadStatus,
        comment: uploadStatus == models.FileUploadStatus.rejected ? '文件不清晰，请重新上传' : null,
      );
    }

    // 根据状态设置已上传文件
    List<models.UploadedFile> uploadedFiles = [];
    if (uploadStatus != models.FileUploadStatus.notUploaded) {
      uploadedFiles = [
        models.UploadedFile(
          fileName: '${fileRequirement.fileType}.pdf',
          filePath: '/storage/emulated/0/Download/${fileRequirement.fileType}.pdf',
          uploadTime: DateTime.now().subtract(const Duration(days: 1)),
        ),
      ];
    }

    return models.FileUploadItem(
      id: fileRequirement.id ?? fileRequirement.fileCode.toString(),
      fileName: fileRequirement.fileType,
      iconData: fileRequirement.iconPath,
      iconColor: Color(fileRequirement.fileStatus.statusTextColor),
      status: uploadStatus,
      statusColor: Color(fileRequirement.fileStatus.statusBackgroundColor),
      statusTextColor: Color(fileRequirement.fileStatus.statusTextColor),
      courseInfo: '2021级市场销售2023-2024实习学年第二学期岗位实习',
      planId: '8',
      fileType: fileRequirement.fileType,
      fileCode: fileRequirement.fileCode,
      templateUrl: uploadStatus == models.FileUploadStatus.notUploaded
          ? 'https://example.com/${fileRequirement.fileType}_template.docx'
          : null,
      description: '这里是${fileRequirement.fileType}相关文件上传的描述文字',
      uploadedFiles: uploadedFiles,
      approvalInfo: approvalInfo,
    );
  }

  /// 打印路由配置信息
  static void printRouteConfiguration(GoRouter router) {
    try {
      final configuration = router.configuration;
      final routes = configuration.routes;

      debugPrint('======= 路由配置信息 =======');
      debugPrint('路由总数: ${routes.length}');

      for (var route in routes) {
        _printRouteInfo(route, '');
      }

      debugPrint('======= 路由配置结束 =======');
    } catch (e) {
      debugPrint('打印路由配置失败: $e');
    }
  }

  /// 递归打印路由信息
  static void _printRouteInfo(RouteBase route, String indent) {
    if (route is GoRoute) {
      final name = route.name != null ? ' (name: ${route.name})' : '';
      debugPrint('$indent- 路径: ${route.path}$name');

      for (var child in route.routes) {
        _printRouteInfo(child, '$indent  ');
      }
    } else if (route is ShellRoute) {
      debugPrint('$indent- ShellRoute');
      for (var child in route.routes) {
        _printRouteInfo(child, '$indent  ');
      }
    }
  }
}

// 错误页面
class ErrorScreen extends StatelessWidget {
  final Exception? error;

  const ErrorScreen({Key? key, this.error}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('页面不存在')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('找不到请求的页面', style: TextStyle(fontSize: 18)),
            if (error != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(error.toString(), style: const TextStyle(color: Colors.red)),
              ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    );
  }
}











