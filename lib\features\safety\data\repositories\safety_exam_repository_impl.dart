/// -----
/// safety_exam_repository_impl.dart
/// 
/// 安全教育考试仓库实现
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/safety/data/datasources/safety_exam_data_source.dart';
import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';
import 'package:flutter_demo/features/safety/domain/models/exam_record_request.dart';
import 'package:flutter_demo/features/safety/domain/repositories/safety_exam_repository.dart';

/// 安全教育考试仓库实现
///
/// 实现安全教育考试仓库接口，通过数据源获取题目数据
class SafetyExamRepositoryImpl implements SafetyExamRepository {
  /// 安全教育考试数据源
  final SafetyExamDataSource dataSource;

  /// 构造函数
  const SafetyExamRepositoryImpl({
    required this.dataSource,
  });

  @override
  Future<List<SafetyExamQuestion>> getExamQuestions({String? planId}) async {
    return await dataSource.getExamQuestions(planId: planId);
  }

  @override
  Future<bool> saveExamRecord(ExamRecordRequest request) async {
    return await dataSource.saveExamRecord(request);
  }
}
