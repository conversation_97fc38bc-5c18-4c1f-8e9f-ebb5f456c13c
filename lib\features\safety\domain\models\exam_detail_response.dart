/// -----
/// exam_detail_response.dart
/// 
/// 考试详情响应模型
///
/// <AUTHOR>
/// @date 2025-06-19
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:convert';
import 'package:equatable/equatable.dart';

/// 考试详情响应模型
/// 
/// 用于接收教师端查看学生考试详情的响应数据
class ExamDetailResponse extends Equatable {
  /// 考试题目记录列表
  final List<ExamQuestionDetailRecord> examQuestionList;
  
  /// 记录ID
  final int id;
  
  /// 是否通过（0=否，1=是）
  final int passStatus;
  
  /// 关联实习计划ID
  final int planId;
  
  /// 实际得分
  final int score;
  
  /// 学生ID
  final int studentId;
  
  /// 学生姓名
  final String studentName;

  const ExamDetailResponse({
    required this.examQuestionList,
    required this.id,
    required this.passStatus,
    required this.planId,
    required this.score,
    required this.studentId,
    required this.studentName,
  });

  @override
  List<Object?> get props => [
    examQuestionList,
    id,
    passStatus,
    planId,
    score,
    studentId,
    studentName,
  ];

  /// 从JSON映射创建对象
  factory ExamDetailResponse.fromJson(Map<String, dynamic> json) {
    return ExamDetailResponse(
      examQuestionList: (json['examQuestionList'] as List)
          .map((e) => ExamQuestionDetailRecord.fromJson(e))
          .toList(),
      id: _parseToInt(json['id']),
      passStatus: _parseToInt(json['passStatus']),
      planId: _parseToInt(json['planId']),
      score: _parseToInt(json['score']),
      studentId: _parseToInt(json['studentId']),
      studentName: json['studentName'] as String,
    );
  }

  /// 安全地将动态类型转换为int
  static int _parseToInt(dynamic value) {
    if (value is int) {
      return value;
    } else if (value is String) {
      return int.tryParse(value) ?? 0;
    } else {
      return 0;
    }
  }

  /// 将对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'examQuestionList': examQuestionList.map((e) => e.toJson()).toList(),
      'id': id,
      'passStatus': passStatus,
      'planId': planId,
      'score': score,
      'studentId': studentId,
      'studentName': studentName,
    };
  }
}

/// 考试题目详情记录模型
/// 
/// 用于表示单个题目的详细答题记录
class ExamQuestionDetailRecord extends Equatable {
  /// 正确答案（如A、AB）
  final String correctAnswer;
  
  /// 题目ID
  final int id;
  
  /// 是否正确（1:正确 0:错误）
  final int isCorrect;
  
  /// 选项JSON字符串，如：[{'A':'xxx','B':'xxx'}]
  final String optionsJson;
  
  /// 分数
  final int score;
  
  /// 学生答案
  final String studentAnswer;
  
  /// 题干（支持富文本）
  final String title;

  const ExamQuestionDetailRecord({
    required this.correctAnswer,
    required this.id,
    required this.isCorrect,
    required this.optionsJson,
    required this.score,
    required this.studentAnswer,
    required this.title,
  });

  @override
  List<Object?> get props => [
    correctAnswer,
    id,
    isCorrect,
    optionsJson,
    score,
    studentAnswer,
    title,
  ];

  /// 从JSON映射创建对象
  factory ExamQuestionDetailRecord.fromJson(Map<String, dynamic> json) {
    return ExamQuestionDetailRecord(
      correctAnswer: json['correctAnswer'] as String,
      id: ExamDetailResponse._parseToInt(json['id']),
      isCorrect: ExamDetailResponse._parseToInt(json['isCorrect']),
      optionsJson: json['optionsJson'] as String,
      score: ExamDetailResponse._parseToInt(json['score']),
      studentAnswer: json['studentAnswer'] as String,
      title: json['title'] as String,
    );
  }

  /// 将对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'correctAnswer': correctAnswer,
      'id': id,
      'isCorrect': isCorrect,
      'optionsJson': optionsJson,
      'score': score,
      'studentAnswer': studentAnswer,
      'title': title,
    };
  }

  /// 解析选项JSON字符串为Map
  Map<String, String> get optionsMap {
    try {
      final optionsJsonList = jsonDecode(optionsJson) as List;
      final optionsMapData = jsonDecode(optionsJsonList.first) as Map<String, dynamic>;
      return optionsMapData.map((key, value) => MapEntry(key, value.toString()));
    } catch (e) {
      return {};
    }
  }

  /// 获取正确答案列表
  List<String> get correctAnswerList {
    return correctAnswer.split('');
  }

  /// 获取学生答案列表
  List<String> get studentAnswerList {
    return studentAnswer.split('');
  }

  /// 判断是否为多选题
  bool get isMultipleChoice => correctAnswerList.length > 1;
}
